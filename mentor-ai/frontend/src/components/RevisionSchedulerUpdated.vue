<template>
  <div class="revision-scheduler">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="gradient-bg"></div>
      <div class="floating-particles">
        <div v-for="i in 20" :key="`particle-${i}`" class="particle"></div>
      </div>
    </div>

    <!-- Main Container -->
    <div class="container">
      <!-- Header -->
      <header class="header">
        <div class="header-content">
          <div class="title-section">
            <div class="title-wrapper">
              <div class="icon-container">
                <div class="icon-glow"></div>
                <font-awesome-icon icon="brain" class="brain-icon" />
              </div>
              <div class="text-content">
                <h1>Sistema de Revisões Espaçadas</h1>
                <p class="subtitle">Metodologia científica para maximizar retenção de conhecimento</p>
              </div>
            </div>
          </div>
          
          <!-- Modern Stats Cards -->
          <div class="stats-container">
            <div class="stat-card">
              <div class="stat-background"></div>
              <div class="stat-content">
                <div class="stat-icon-wrapper">
                  <font-awesome-icon icon="calendar-check" class="stat-icon" />
                </div>
                <div class="stat-info">
                  <span class="stat-value" :data-value="totalRevisoes">{{ totalRevisoes || 0 }}</span>
                  <span class="stat-label">Revisões Agendadas</span>
                  <div class="stat-details">
                    <span class="detail-item">
                      <font-awesome-icon icon="clock" class="detail-icon" />
                      {{ revisoesHoje || 0 }} hoje
                    </span>
                    <span class="detail-item">
                      <font-awesome-icon icon="calendar-week" class="detail-icon" />
                      {{ revisoesSemana || 0 }} esta semana
                    </span>
                  </div>
                </div>
                <div class="stat-trend positive">
                  <font-awesome-icon icon="arrow-up" />
                  <span>12%</span>
                  <span class="trend-label">vs. mês anterior</span>
                </div>
              </div>
            </div>

            <div class="stat-card featured">
              <div class="stat-background"></div>
              <div class="stat-content">
                <div class="stat-icon-wrapper">
                  <font-awesome-icon icon="chart-line" class="stat-icon" />
                </div>
                <div class="stat-info">
                  <div class="stat-value-wrapper">
                    <span class="stat-value" :data-value="taxaRetencao">{{ taxaRetencao || 0 }}</span>
                    <span class="stat-unit">%</span>
                  </div>
                  <span class="stat-label">Taxa de Retenção</span>
                  <div class="stat-details">
                    <span class="detail-item">
                      <font-awesome-icon icon="trophy" class="detail-icon gold" />
                      Meta: 90%
                    </span>
                    <span class="detail-item">
                      <font-awesome-icon icon="chart-bar" class="detail-icon" />
                      Média: {{ mediaRetencao || 0 }}%
                    </span>
                  </div>
                </div>
                <div class="progress-ring">
                  <svg width="60" height="60">
                    <circle cx="30" cy="30" r="25" fill="none" stroke="#334155" stroke-width="5"/>
                    <circle cx="30" cy="30" r="25" fill="none" stroke="url(#gradient)" stroke-width="5"
                            stroke-linecap="round"
                            :stroke-dasharray="`${taxaRetencao * 1.57} 157`"
                            transform="rotate(-90 30 30)"/>
                    <defs>
                      <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stop-color="#818cf8"/>
                        <stop offset="100%" stop-color="#c084fc"/>
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-background"></div>
              <div class="stat-content">
                <div class="stat-icon-wrapper">
                  <font-awesome-icon icon="fire" class="stat-icon" />
                </div>
                <div class="stat-info">
                  <span class="stat-value" :data-value="diasEstudados">{{ diasEstudados || 0 }}</span>
                  <span class="stat-label">Dias de Estudo</span>
                  <div class="stat-details">
                    <span class="detail-item">
                      <font-awesome-icon icon="medal" class="detail-icon" />
                      Maior sequência: {{ maiorSequencia || 0 }} dias
                    </span>
                    <span class="detail-item">
                      <font-awesome-icon icon="star" class="detail-icon" />
                      Nível: {{ nivelEstudante }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main Content Grid -->
      <div class="content-grid">
        <!-- Estudo Teórico Section -->
        <section class="study-section teorico-section">
          <div class="section-header">
            <div class="section-title-wrapper">
              <div class="section-icon-container">
                <div class="section-icon-glow"></div>
                <font-awesome-icon icon="file-lines" class="section-icon" />
              </div>
              <div class="section-text-content">
                <h2>Registro de Estudo Teórico</h2>
                <p>Registre seu primeiro contato com o conteúdo</p>
              </div>
            </div>
          </div>

          <form @submit.prevent="registrarEstudoTeorico" class="teorico-form">
            <div class="form-group">
              <label>
                <font-awesome-icon icon="bookmark" />
                Matéria / Tópico
              </label>
              <input 
                v-model="estudoTeorico.materia" 
                type="text" 
                placeholder="Ex: Anatomia - Sistema Cardiovascular"
                required
              />
            </div>

            <div class="form-group">
              <label>
                <font-awesome-icon icon="calendar" />
                Data do Estudo Teórico
              </label>
              <input 
                v-model="estudoTeorico.data" 
                type="date" 
                required
              />
            </div>

            <div class="form-group">
              <label>
                <font-awesome-icon icon="signal" />
                Grau de Dificuldade
              </label>
              <div class="difficulty-selector">
                <button 
                  type="button"
                  :class="['diff-option', { active: estudoTeorico.dificuldade === 'Fácil' }]"
                  @click="estudoTeorico.dificuldade = 'Fácil'"
                >
                  <font-awesome-icon icon="smile" />
                  <span>Fácil</span>
                  <small>Primeiro contato em 2 dias</small>
                </button>
                <button 
                  type="button"
                  :class="['diff-option', { active: estudoTeorico.dificuldade === 'Difícil' }]"
                  @click="estudoTeorico.dificuldade = 'Difícil'"
                >
                  <font-awesome-icon icon="frown" />
                  <span>Difícil</span>
                  <small>Primeiro contato em 1 dia</small>
                </button>
              </div>
            </div>

            <div class="form-preview" v-if="estudoTeorico.dificuldade">
              <div class="preview-card">
                <font-awesome-icon icon="calendar-check" class="preview-icon" />
                <div>
                  <strong>Primeiro Contato Agendado:</strong>
                  <p>{{ calcularPrimeiroContato() }}</p>
                </div>
              </div>
            </div>

            <button type="submit" class="submit-btn">
              <font-awesome-icon icon="plus-circle" />
              Registrar Estudo Teórico
            </button>
          </form>
        </section>

        <!-- Revisão Prática Section -->
        <section class="study-section pratica-section">
          <div class="section-header">
            <div class="section-title-wrapper">
              <div class="section-icon-container">
                <div class="section-icon-glow"></div>
                <font-awesome-icon icon="clipboard-check" class="section-icon" />
              </div>
              <div class="section-text-content">
                <h2>Registro de Revisão Prática</h2>
                <p>Registre seu desempenho em questões</p>
              </div>
            </div>
          </div>

          <form @submit.prevent="registrarRevisaoPratica" class="pratica-form">
            <div class="form-group">
              <label>
                <font-awesome-icon icon="bookmark" />
                Matéria / Tópico
              </label>
              <select v-model="revisaoPratica.materiaId" required>
                <option value="">Selecione a matéria</option>
                <option v-for="estudo in estudosTeoricosPendentes" :key="estudo.id" :value="estudo.id">
                  {{ estudo.materia }} - {{ formatDate(estudo.primeiroContato) }}
                </option>
              </select>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>
                  <font-awesome-icon icon="clipboard-list" />
                  Total de Questões
                </label>
                <input 
                  v-model.number="revisaoPratica.totalQuestoes" 
                  type="number" 
                  min="20"
                  max="30"
                  placeholder="20-30"
                  required
                />
              </div>

              <div class="form-group">
                <label>
                  <font-awesome-icon icon="check-circle" />
                  Questões Corretas
                </label>
                <input 
                  v-model.number="revisaoPratica.acertos" 
                  type="number" 
                  min="0"
                  :max="revisaoPratica.totalQuestoes"
                  required
                />
              </div>
            </div>

            <div class="performance-preview" v-if="percentualCalculado">
              <div class="performance-meter">
                <div class="meter-fill" :style="{ width: percentualCalculado + '%' }"></div>
                <span class="meter-label">{{ percentualCalculado }}%</span>
              </div>
              
              <div class="next-revision-preview">
                <font-awesome-icon icon="clock" />
                <div>
                  <strong>Próxima Revisão:</strong>
                  <p>{{ proximaRevisaoTexto }}</p>
                </div>
              </div>
            </div>

            <button type="submit" class="submit-btn" :disabled="!percentualCalculado">
              <font-awesome-icon icon="chart-line" />
              Registrar Desempenho
            </button>
          </form>
        </section>

        <!-- Grades de Revisão -->
        <section class="revision-grid-section">
          <div class="section-header">
            <div class="section-title-wrapper">
              <div class="section-icon-container">
                <div class="section-icon-glow"></div>
                <font-awesome-icon icon="th" class="section-icon" />
              </div>
              <div class="section-text-content">
                <h2>Grades de Revisão</h2>
              </div>
            </div>
            <div class="view-controls">
              <button 
                :class="['view-btn', { active: viewMode === 'teorico' }]"
                @click="viewMode = 'teorico'"
              >
                <font-awesome-icon icon="book" />
                Teórico
              </button>
              <button 
                :class="['view-btn', { active: viewMode === 'pratico' }]"
                @click="viewMode = 'pratico'"
              >
                <font-awesome-icon icon="tasks" />
                Prático
              </button>
              <button 
                :class="['view-btn', { active: viewMode === 'calendario' }]"
                @click="viewMode = 'calendario'"
              >
                <font-awesome-icon icon="calendar" />
                Calendário
              </button>
            </div>
          </div>

          <!-- Grade Teórico -->
          <div v-if="viewMode === 'teorico'" class="revision-table">
            <table>
              <thead>
                <tr>
                  <th>Matéria</th>
                  <th>Data Estudo</th>
                  <th>Dificuldade</th>
                  <th>Primeiro Contato</th>
                  <th>Status</th>
                  <th>Ações</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="estudo in estudosTeoricos" :key="estudo.id">
                  <td class="subject-cell">{{ estudo.materia }}</td>
                  <td>{{ formatDate(estudo.data) }}</td>
                  <td>
                    <span :class="['badge', 'badge-' + estudo.dificuldade.toLowerCase()]">
                      {{ estudo.dificuldade }}
                    </span>
                  </td>
                  <td class="date-cell">{{ formatDate(estudo.primeiroContato) }}</td>
                  <td>
                    <span :class="['status', getStatus(estudo.primeiroContato)]">
                      {{ getStatusText(estudo.primeiroContato) }}
                    </span>
                  </td>
                  <td>
                    <button @click="marcarComoConcluido(estudo.id)" class="action-btn">
                      <font-awesome-icon icon="check" />
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Grade Prático -->
          <div v-if="viewMode === 'pratico'" class="revision-table">
            <table>
              <thead>
                <tr>
                  <th>Matéria</th>
                  <th>Data Revisão</th>
                  <th>Desempenho</th>
                  <th>Intervalo</th>
                  <th>Próxima Revisão</th>
                  <th>Ações</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="revisao in revisoesPraticas" :key="revisao.id">
                  <td class="subject-cell">{{ revisao.materia }}</td>
                  <td>{{ formatDate(revisao.data) }}</td>
                  <td>
                    <div class="performance-cell">
                      <div class="mini-meter">
                        <div class="mini-fill" :style="{ width: revisao.percentual + '%' }"></div>
                      </div>
                      <span>{{ revisao.percentual }}%</span>
                    </div>
                  </td>
                  <td>{{ revisao.intervalo }} dias</td>
                  <td class="date-cell">{{ formatDate(revisao.proximaRevisao) }}</td>
                  <td>
                    <button @click="marcarRevisaoConcluida(revisao.id)" class="action-btn">
                      <font-awesome-icon icon="check" />
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Calendário -->
          <div v-if="viewMode === 'calendario'" class="calendar-view">
            <div class="calendar-header">
              <button @click="previousMonth" class="nav-btn">
                <font-awesome-icon icon="chevron-left" />
              </button>
              <h3>{{ currentMonthYear }}</h3>
              <button @click="nextMonth" class="nav-btn">
                <font-awesome-icon icon="chevron-right" />
              </button>
            </div>
            <div class="calendar-grid">
              <div v-for="day in weekDays" :key="day" class="weekday">{{ day }}</div>
              <div 
                v-for="(day, index) in calendarDays" 
                :key="index"
                :class="['calendar-day', { 
                  'other-month': !day.currentMonth,
                  'today': day.isToday,
                  'has-revision': day.revisions.length > 0
                }]"
              >
                <span class="day-number">{{ day.date }}</span>
                <div v-if="day.revisions.length > 0" class="day-revisions">
                  <div 
                    v-for="rev in day.revisions" 
                    :key="rev.id"
                    :class="['revision-dot', rev.type]"
                    :title="rev.materia"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script>
import { useToast } from 'vue-toastification';

export default {
  name: 'RevisionSchedulerUpdated',
  
  setup() {
    const toast = useToast();
    return { toast };
  },
  
  data() {
    return {
      // Estado do formulário teórico
      estudoTeorico: {
        materia: '',
        data: '',
        dificuldade: ''
      },
      
      // Estado do formulário prático
      revisaoPratica: {
        materiaId: '',
        totalQuestoes: 30,
        acertos: 0
      },
      
      // Dados armazenados
      estudosTeoricos: [],
      revisoesPraticas: [],
      
      // Controles de visualização
      viewMode: 'teorico',
      currentMonth: new Date(),
      
      // Estatísticas
      totalRevisoes: 0,
      taxaRetencao: 85,
      diasEstudados: 0,
      revisoesHoje: 0,
      revisoesSemana: 0,
      mediaRetencao: 82,
      maiorSequencia: 0,
      nivelEstudante: 'Iniciante'
    }
  },
  
  computed: {
    percentualCalculado() {
      if (this.revisaoPratica.totalQuestoes > 0) {
        return Math.round((this.revisaoPratica.acertos / this.revisaoPratica.totalQuestoes) * 100);
      }
      return 0;
    },
    
    proximaRevisaoTexto() {
      const dias = this.calcularIntervaloRevisao(this.percentualCalculado);
      return `Em ${dias} dias (${this.addDays(new Date(), dias).toLocaleDateString('pt-BR')})`;
    },
    
    estudosTeoricosPendentes() {
      return this.estudosTeoricos.filter(e => !e.concluido);
    },
    
    currentMonthYear() {
      return this.currentMonth.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' });
    },
    
    weekDays() {
      return ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
    },
    
    calendarDays() {
      const year = this.currentMonth.getFullYear();
      const month = this.currentMonth.getMonth();
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);
      const startCalendar = new Date(firstDay);
      startCalendar.setDate(startCalendar.getDate() - firstDay.getDay());
      
      const days = [];
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      for (let i = 0; i < 42; i++) {
        const current = new Date(startCalendar);
        current.setDate(startCalendar.getDate() + i);
        
        const dayRevisions = this.getRevisionsForDate(current);
        
        days.push({
          date: current.getDate(),
          currentMonth: current.getMonth() === month,
          isToday: current.getTime() === today.getTime(),
          revisions: dayRevisions
        });
      }
      
      return days;
    }
  },
  
  methods: {
    // Cálculo do primeiro contato baseado na dificuldade
    calcularPrimeiroContato() {
      if (!this.estudoTeorico.data || !this.estudoTeorico.dificuldade) return '';
      
      const dataEstudo = new Date(this.estudoTeorico.data);
      const dias = this.estudoTeorico.dificuldade === 'Fácil' ? 2 : 1;
      
      return this.addDays(dataEstudo, dias).toLocaleDateString('pt-BR');
    },
    
    // Cálculo do intervalo de revisão baseado no desempenho
    calcularIntervaloRevisao(percentual) {
      if (percentual <= 50) return 2;
      if (percentual <= 55) return 7;
      if (percentual <= 60) return 14;
      if (percentual <= 65) return 18;
      if (percentual <= 75) return 24;
      if (percentual <= 80) return 30;
      return 35;
    },
    
    // Registrar estudo teórico
    registrarEstudoTeorico() {
      const dataEstudo = new Date(this.estudoTeorico.data);
      const diasAte = this.estudoTeorico.dificuldade === 'Fácil' ? 2 : 1;
      const primeiroContato = this.addDays(dataEstudo, diasAte);
      
      const novoEstudo = {
        id: Date.now(),
        ...this.estudoTeorico,
        primeiroContato: primeiroContato,
        concluido: false
      };
      
      this.estudosTeoricos.push(novoEstudo);
      this.salvarDados();
      this.atualizarEstatisticas();
      
      // Limpar formulário
      this.estudoTeorico = {
        materia: '',
        data: '',
        dificuldade: ''
      };
      
      this.toast.success('Estudo teórico registrado com sucesso!');
    },
    
    // Registrar revisão prática
    registrarRevisaoPratica() {
      const estudo = this.estudosTeoricos.find(e => e.id === parseInt(this.revisaoPratica.materiaId));
      if (!estudo) return;
      
      const intervalo = this.calcularIntervaloRevisao(this.percentualCalculado);
      const proximaRevisao = this.addDays(new Date(), intervalo);
      
      const novaRevisao = {
        id: Date.now(),
        materiaId: this.revisaoPratica.materiaId,
        materia: estudo.materia,
        data: new Date(),
        totalQuestoes: this.revisaoPratica.totalQuestoes,
        acertos: this.revisaoPratica.acertos,
        percentual: this.percentualCalculado,
        intervalo: intervalo,
        proximaRevisao: proximaRevisao,
        concluida: false
      };
      
      this.revisoesPraticas.push(novaRevisao);
      estudo.concluido = true;
      this.salvarDados();
      this.atualizarEstatisticas();
      
      // Limpar formulário
      this.revisaoPratica = {
        materiaId: '',
        totalQuestoes: 30,
        acertos: 0
      };
      
      this.toast.success('Revisão prática registrada! Próxima revisão agendada.');
    },
    
    // Utilidades
    addDays(date, days) {
      const result = new Date(date);
      result.setDate(result.getDate() + days);
      return result;
    },
    
    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleDateString('pt-BR');
    },
    
    getStatus(date) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const compareDate = new Date(date);
      compareDate.setHours(0, 0, 0, 0);
      
      if (compareDate < today) return 'overdue';
      if (compareDate.getTime() === today.getTime()) return 'today';
      return 'upcoming';
    },
    
    getStatusText(date) {
      const status = this.getStatus(date);
      if (status === 'overdue') return 'Atrasado';
      if (status === 'today') return 'Hoje';
      return 'Agendado';
    },
    
    getRevisionsForDate(date) {
      const revisions = [];
      const dateStr = date.toDateString();
      
      // Verificar estudos teóricos
      this.estudosTeoricos.forEach(estudo => {
        if (new Date(estudo.primeiroContato).toDateString() === dateStr) {
          revisions.push({
            id: estudo.id,
            type: 'teorico',
            materia: estudo.materia
          });
        }
      });
      
      // Verificar revisões práticas
      this.revisoesPraticas.forEach(revisao => {
        if (new Date(revisao.proximaRevisao).toDateString() === dateStr) {
          revisions.push({
            id: revisao.id,
            type: 'pratico',
            materia: revisao.materia
          });
        }
      });
      
      return revisions;
    },
    
    // Navegação do calendário
    previousMonth() {
      this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() - 1);
    },
    
    nextMonth() {
      this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1);
    },
    
    // Ações
    marcarComoConcluido(id) {
      const estudo = this.estudosTeoricos.find(e => e.id === id);
      if (estudo) {
        estudo.concluido = true;
        this.salvarDados();
      }
    },
    
    marcarRevisaoConcluida(id) {
      const revisao = this.revisoesPraticas.find(r => r.id === id);
      if (revisao) {
        revisao.concluida = true;
        this.salvarDados();
      }
    },
    
    // Persistência de dados
    salvarDados() {
      localStorage.setItem('estudosTeoricos', JSON.stringify(this.estudosTeoricos));
      localStorage.setItem('revisoesPraticas', JSON.stringify(this.revisoesPraticas));
    },
    
    carregarDados() {
      const estudos = localStorage.getItem('estudosTeoricos');
      const revisoes = localStorage.getItem('revisoesPraticas');
      
      if (estudos) this.estudosTeoricos = JSON.parse(estudos);
      if (revisoes) this.revisoesPraticas = JSON.parse(revisoes);
      
      this.atualizarEstatisticas();
    },
    
    atualizarEstatisticas() {
      this.totalRevisoes = this.estudosTeoricos.length + this.revisoesPraticas.length;
      
      const diasUnicos = new Set();
      this.estudosTeoricos.forEach(e => diasUnicos.add(new Date(e.data).toDateString()));
      this.revisoesPraticas.forEach(r => diasUnicos.add(new Date(r.data).toDateString()));
      this.diasEstudados = diasUnicos.size;
      
      // Calcular taxa de retenção média
      if (this.revisoesPraticas.length > 0) {
        const somaPercentuais = this.revisoesPraticas.reduce((sum, r) => sum + r.percentual, 0);
        this.taxaRetencao = Math.round(somaPercentuais / this.revisoesPraticas.length);
        this.mediaRetencao = Math.round(somaPercentuais / this.revisoesPraticas.length * 0.96); // Média ligeiramente menor
      }
      
      // Calcular revisões hoje
      const hoje = new Date().toDateString();
      this.revisoesHoje = this.estudosTeoricos.filter(e => 
        new Date(e.primeiroContato).toDateString() === hoje && !e.concluido
      ).length + this.revisoesPraticas.filter(r => 
        new Date(r.proximaRevisao).toDateString() === hoje && !r.concluida
      ).length;
      
      // Calcular revisões da semana
      const inicioSemana = new Date();
      inicioSemana.setDate(inicioSemana.getDate() - inicioSemana.getDay());
      const fimSemana = new Date(inicioSemana);
      fimSemana.setDate(fimSemana.getDate() + 6);
      
      this.revisoesSemana = this.estudosTeoricos.filter(e => {
        const data = new Date(e.primeiroContato);
        return data >= inicioSemana && data <= fimSemana && !e.concluido;
      }).length + this.revisoesPraticas.filter(r => {
        const data = new Date(r.proximaRevisao);
        return data >= inicioSemana && data <= fimSemana && !r.concluida;
      }).length;
      
      // Calcular maior sequência e nível
      this.calcularSequenciaENivel();
    },
    
    calcularSequenciaENivel() {
      // Simular maior sequência baseado nos dias estudados
      this.maiorSequencia = Math.max(this.diasEstudados, Math.floor(this.diasEstudados * 1.5));
      
      // Determinar nível baseado em dias estudados e taxa de retenção
      if (this.diasEstudados >= 100 && this.taxaRetencao >= 90) {
        this.nivelEstudante = 'Mestre';
      } else if (this.diasEstudados >= 50 && this.taxaRetencao >= 80) {
        this.nivelEstudante = 'Avançado';
      } else if (this.diasEstudados >= 20 && this.taxaRetencao >= 70) {
        this.nivelEstudante = 'Intermediário';
      } else if (this.diasEstudados >= 7) {
        this.nivelEstudante = 'Aprendiz';
      } else {
        this.nivelEstudante = 'Iniciante';
      }
    },
    
    getStreakMessage() {
      if (this.diasEstudados === 0) return 'Comece hoje!';
      if (this.diasEstudados < 3) return '';
      if (this.diasEstudados < 7) return 'Continue assim!';
      if (this.diasEstudados < 14) return '🔥 Uma semana!';
      if (this.diasEstudados < 30) return '🔥 Em chamas!';
      if (this.diasEstudados < 60) return '🔥🔥 Impressionante!';
      if (this.diasEstudados < 100) return '🔥🔥🔥 Incrível!';
      return '🏆 Lendário!';
    },
    
    // Animações de entrada
    animateElements() {
      // Animar valores dos stats
      this.$nextTick(() => {
        const statValues = document.querySelectorAll('.stat-value');
        statValues.forEach((el, index) => {
          const finalValue = parseInt(el.getAttribute('data-value') || el.textContent);
          const duration = 1500;
          const startTime = Date.now();
          const startValue = 0;
          
          const animate = () => {
            const currentTime = Date.now();
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.floor(startValue + (finalValue - startValue) * easeOutQuart);
            
            el.textContent = currentValue;
            
            if (progress < 1) {
              requestAnimationFrame(animate);
            } else {
              el.textContent = finalValue;
            }
          };
          
          setTimeout(() => animate(), index * 200);
        });
      });
    }
  },
  
  mounted() {
    this.carregarDados();
    this.animateElements();
  }
}
</script>

<style scoped>
/* Reset and Variables */
:root {
  --primary: #6366f1;
  --secondary: #8b5cf6;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --dark: #1e293b;
  --darker: #0f172a;
  --light: #f8fafc;
  --text: #94a3b8;
  --border: #334155;
}

* {
  box-sizing: border-box;
}

/* Main Container */
.revision-scheduler {
  min-height: 100vh;
  background: var(--darker);
  color: var(--light);
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.gradient-bg {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
}

.floating-particles {
  position: absolute;
  inset: 0;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--primary);
  border-radius: 50%;
  opacity: 0.3;
  animation: float 20s infinite ease-in-out;
}

/* Distribute particles randomly */
.particle:nth-child(1) { left: 5%; top: 10%; }
.particle:nth-child(2) { left: 15%; top: 80%; }
.particle:nth-child(3) { left: 25%; top: 30%; }
.particle:nth-child(4) { left: 35%; top: 60%; }
.particle:nth-child(5) { left: 45%; top: 20%; }
.particle:nth-child(6) { left: 55%; top: 90%; }
.particle:nth-child(7) { left: 65%; top: 40%; }
.particle:nth-child(8) { left: 75%; top: 70%; }
.particle:nth-child(9) { left: 85%; top: 15%; }
.particle:nth-child(10) { left: 95%; top: 50%; }
.particle:nth-child(11) { left: 10%; top: 45%; }
.particle:nth-child(12) { left: 20%; top: 65%; }
.particle:nth-child(13) { left: 30%; top: 85%; }
.particle:nth-child(14) { left: 40%; top: 25%; }
.particle:nth-child(15) { left: 50%; top: 75%; }
.particle:nth-child(16) { left: 60%; top: 35%; }
.particle:nth-child(17) { left: 70%; top: 95%; }
.particle:nth-child(18) { left: 80%; top: 55%; }
.particle:nth-child(19) { left: 90%; top: 5%; }
.particle:nth-child(20) { left: 100%; top: 85%; }

.particle:nth-child(odd) {
  animation-duration: 25s;
  background: var(--secondary);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  33% {
    transform: translateY(-100px) translateX(50px);
  }
  66% {
    transform: translateY(-50px) translateX(-50px);
  }
}

/* Container */
.container {
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Header - Clean Modern Design */
.header {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95), rgba(51, 65, 85, 0.95));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1.5rem;
  padding: 2.5rem;
  margin-bottom: 2.5rem;
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(129, 140, 248, 0.1) 0%, transparent 70%);
  animation: rotate-slow 30s linear infinite;
}

@keyframes rotate-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.header-content {
  position: relative;
  z-index: 1;
}

/* Title Section */
.title-section {
  margin-bottom: 2.5rem;
}

.title-wrapper {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.icon-container {
  position: relative;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 0.625rem;
}

.icon-glow {
  display: none; /* Remove glow for minimal design */
}

.brain-icon {
  font-size: 1.25rem;
  color: var(--text);
  opacity: 0.8;
}

.text-content h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  color: var(--light);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.subtitle {
  color: var(--text);
  margin: 0.5rem 0 0;
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Stats Container */
.stats-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

/* Stat Cards */
.stat-card {
  position: relative;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.stat-card:hover::before {
  transform: translateX(0);
}

.stat-card:hover {
  transform: translateY(-5px);
  border-color: rgba(129, 140, 248, 0.3);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.stat-card.featured {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border-color: rgba(129, 140, 248, 0.3);
}

.stat-background {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at top right, rgba(129, 140, 248, 0.1) 0%, transparent 60%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover .stat-background {
  opacity: 1;
}

.stat-content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Stat Icons */
.stat-icon-wrapper {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon {
  font-size: 0.875rem;
  color: var(--text);
  opacity: 0.7;
}

/* Stat Values */
.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 800;
  color: var(--light);
  line-height: 1;
  transition: all 0.3s ease;
}

.stat-value-wrapper {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.stat-unit {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: var(--text);
  margin-top: 0.5rem;
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* Stat Details */
.stat-details {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--text);
  font-weight: 400;
}

.detail-icon {
  font-size: 0.75rem;
  color: var(--text);
  opacity: 0.5;
}

.detail-icon.gold {
  color: var(--text);
  opacity: 0.6;
}

/* Progress Ring */
.progress-ring {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.progress-ring svg {
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.stat-card:hover .progress-ring svg {
  transform: scale(1);
}

/* Stat Trend */
.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
  border-radius: 9999px;
  width: fit-content;
}

.trend-label {
  font-size: 0.625rem;
  font-weight: 400;
  opacity: 0.8;
}

.stat-trend.positive {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.stat-trend.negative {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}


/* Value Animation on Hover */
.stat-card:hover .stat-value {
  transform: scale(1.05);
  color: var(--primary);
}

/* Responsive Updates */
@media (max-width: 1024px) {
  .stats-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .stat-card {
    display: flex;
    align-items: center;
  }
  
  .stat-content {
    flex-direction: row;
    align-items: center;
    width: 100%;
  }
  
  .stat-info {
    flex: 1;
  }
  
  .stat-details {
    flex-direction: row;
    gap: 1rem;
    border-top: none;
    padding-top: 0;
    margin-top: 0.5rem;
  }
  
  .progress-ring {
    position: static;
    margin-left: auto;
  }
  
  .stat-value {
    font-size: 1.75rem;
  }
  
  .stat-unit {
    font-size: 1rem;
  }
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Study Sections */
.study-section {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95), rgba(51, 65, 85, 0.95));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.section-header {
  margin-bottom: 2rem;
}

/* Section Title Wrapper */
.section-title-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

/* Section Icon Container - Minimal style */
.section-icon-container {
  position: relative;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
}

.section-icon-glow {
  display: none; /* Remove glow for minimal design */
}

.section-icon {
  font-size: 1rem;
  color: var(--text);
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.section-header:hover .section-icon {
  opacity: 0.9;
}

/* Section Text Content */
.section-text-content h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--light);
}

.section-text-content p {
  color: var(--text);
  margin: 0.25rem 0 0;
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Forms */
.teorico-form,
.pratica-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text);
  margin-bottom: 0.25rem;
}

.form-group label .fa-icon {
  font-size: 0.75rem;
  color: var(--text);
  opacity: 0.5;
}

.form-group input,
.form-group select {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.625rem;
  padding: 0.75rem 1rem;
  color: var(--light);
  font-size: 0.875rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Difficulty Selector */
.difficulty-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.diff-option {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  backdrop-filter: blur(10px);
}

.diff-option .fa-icon {
  font-size: 1rem;
  margin-bottom: 0.25rem;
  color: var(--text);
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.diff-option:hover .fa-icon {
  opacity: 0.8;
}

.diff-option:hover {
  border-color: rgba(129, 140, 248, 0.5);
  background: rgba(99, 102, 241, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.diff-option.active {
  border-color: rgba(129, 140, 248, 0.5);
  background: rgba(99, 102, 241, 0.15);
  box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
}

.diff-option span {
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--light);
}

.diff-option small {
  font-size: 0.75rem;
  color: var(--text);
  opacity: 0.8;
}

/* Preview Cards */
.form-preview,
.performance-preview {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border: 1px solid rgba(129, 140, 248, 0.3);
  border-radius: 0.75rem;
  padding: 1rem;
  backdrop-filter: blur(10px);
}

.preview-card {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.preview-icon {
  font-size: 1.125rem;
  color: var(--text);
  opacity: 0.6;
}

/* Performance Meter */
.performance-meter {
  position: relative;
  height: 1.75rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.meter-fill {
  position: absolute;
  height: 100%;
  background: linear-gradient(90deg, var(--danger), var(--warning), var(--success));
  transition: width 0.5s ease;
}

.meter-label {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.813rem;
  color: var(--light);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.next-revision-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}

/* Submit Button */
.submit-btn {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 0.875rem 1.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.submit-btn .fa-icon {
  font-size: 0.813rem;
  opacity: 0.9;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Revision Grid Section */
.revision-grid-section {
  grid-column: 1 / -1;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95), rgba(51, 65, 85, 0.95));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.view-controls {
  display: flex;
  gap: 0.5rem;
}

.view-btn {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.view-btn .fa-icon {
  font-size: 0.75rem;
  opacity: 0.6;
}

.view-btn:hover {
  border-color: var(--primary);
  color: var(--light);
}

.view-btn.active {
  background: var(--primary);
  border-color: var(--primary);
  color: white;
}

/* Tables */
.revision-table {
  overflow-x: auto;
  margin-top: 2rem;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 0.75rem;
  padding: 0.5rem;
  backdrop-filter: blur(10px);
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  background: var(--dark);
  padding: 0.875rem 1rem;
  text-align: left;
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--text);
  border-bottom: 2px solid var(--border);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

td {
  padding: 0.875rem 1rem;
  font-size: 0.875rem;
  color: var(--light);
  border-bottom: 1px solid var(--border);
}

tr:hover {
  background: rgba(99, 102, 241, 0.05);
}

/* Badges */
.badge {
  padding: 0.25rem 0.625rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  letter-spacing: 0.025em;
}

.badge-fácil {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success);
}

.badge-difícil {
  background: rgba(239, 68, 68, 0.2);
  color: var(--danger);
}

/* Status */
.status {
  padding: 0.25rem 0.625rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  letter-spacing: 0.025em;
}

.status.overdue {
  background: rgba(239, 68, 68, 0.2);
  color: var(--danger);
}

.status.today {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning);
}

.status.upcoming {
  background: rgba(99, 102, 241, 0.2);
  color: var(--primary);
}

/* Performance Cell */
.performance-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.mini-meter {
  flex: 1;
  height: 0.375rem;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 0.25rem;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.mini-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--danger), var(--warning), var(--success));
}

/* Action Button */
.action-btn {
  background: rgba(255, 255, 255, 0.08);
  color: var(--text);
  border: 1px solid rgba(255, 255, 255, 0.1);
  width: 32px;
  height: 32px;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  opacity: 0.7;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.12);
  opacity: 1;
}

/* Calendar View */
.calendar-view {
  margin-top: 2rem;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.calendar-header h3 {
  margin: 0;
  text-transform: capitalize;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--light);
}

.nav-btn {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--light);
  padding: 0.5rem 0.875rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-size: 0.875rem;
}

.nav-btn:hover {
  border-color: var(--primary);
  color: var(--primary);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: var(--border);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  overflow: hidden;
}

.weekday {
  background: rgba(30, 41, 59, 0.8);
  padding: 0.875rem;
  text-align: center;
  font-weight: 500;
  font-size: 0.75rem;
  color: var(--text);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.calendar-day {
  background: rgba(30, 41, 59, 0.3);
  min-height: 80px;
  padding: 0.625rem;
  position: relative;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.calendar-day:hover {
  background: rgba(99, 102, 241, 0.1);
}

.calendar-day.other-month {
  opacity: 0.3;
}

.calendar-day.today {
  background: rgba(99, 102, 241, 0.2);
}

.calendar-day.has-revision {
  border: 2px solid var(--primary);
}

.day-number {
  font-weight: 500;
  font-size: 0.813rem;
  color: var(--light);
}

.day-revisions {
  display: flex;
  gap: 0.25rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.revision-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.revision-dot.teorico {
  background: var(--primary);
}

.revision-dot.pratico {
  background: var(--secondary);
}

/* Responsive */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-summary {
    justify-content: center;
  }
}

@media (max-width: 640px) {
  .container {
    padding: 1rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .difficulty-selector {
    grid-template-columns: 1fr;
  }
  
  .view-controls {
    flex-wrap: wrap;
  }
  
  .calendar-grid {
    font-size: 0.75rem;
  }
  
  .calendar-day {
    min-height: 60px;
  }
}

/* Toast Animations */
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.toast-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

/* Entrance Animations */
.header {
  animation: slideDown 0.8s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.title-wrapper {
  animation: fadeInLeft 1s ease-out 0.2s both;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.stat-card {
  animation: fadeInUp 0.8s ease-out both;
}

.stat-card:nth-child(1) {
  animation-delay: 0.4s;
}

.stat-card:nth-child(2) {
  animation-delay: 0.6s;
}

.stat-card:nth-child(3) {
  animation-delay: 0.8s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover Animations Enhancement */
.stat-icon-wrapper {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.stat-value {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Particle Animations Enhancement */
.particle {
  opacity: 0;
  animation: float 20s infinite ease-in-out, fadeIn 2s ease-out forwards;
}

@keyframes fadeIn {
  to {
    opacity: 0.3;
  }
}

.particle:nth-child(even) {
  animation-delay: 0.5s;
}

.particle:nth-child(3n) {
  animation-delay: 1s;
}

.particle:nth-child(5n) {
  animation-delay: 1.5s;
}

/* Loading State */
.stat-value[data-loading="true"] {
  background: linear-gradient(90deg, var(--text) 0%, var(--light) 50%, var(--text) 100%);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>